﻿console.log('Starting automatic implementation...');
const fs = require('fs');
const path = require('path');

// Step 1: Fix storage.ts
console.log('Step 1: Fixing storage.ts');
try {
  let content = fs.readFileSync('server/storage.ts', 'utf8');
  console.log('File read successfully, length:', content.length);
  
  // Simple approach: add methods before the last closing brace
  const lastBrace = content.lastIndexOf('});
  if (lastBrace > 0) {
    const beforeBrace = content.substring(0, lastBrace);
    const afterBrace = content.substring(lastBrace);
    
    const newMethods = '  async createStockTransfer(transferData) { const [transfer] = await db.insert(stockTransfers).values(transferData).returning(); return transfer; }\\n\\n';
    
    const newContent = beforeBrace + newMethods + afterBrace;
    fs.writeFileSync('server/storage.ts', newContent, 'utf8');
    console.log('storage.ts updated successfully');
  }
} catch (error) {
  console.error('Error:', error.message);
}
